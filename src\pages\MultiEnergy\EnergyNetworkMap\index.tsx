import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.sass';

// 设置 Mapbox 访问令牌（使用公共令牌或配置环境变量）
mapboxgl.accessToken =
  'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

interface EnergyNetworkMapProps {
  className?: string;
}

// 数据接口定义
interface FaultInfo {
  level: 1 | 2 | 3; // 故障等级：1-严重，2-中等，3-轻微
  title: string; // 响应级别标题
  occurTime: string; // 发生时间
  description: string; // 故障描述
  location: string; // 故障位置
  currentStatus: '处理中' | '待处理' | '已解决'; // 当前状态
}

interface PipelineData {
  id: string;
  type: 'heat' | 'gas';
  coordinates: [number, number][];
  flow: number;
  direction: 'forward' | 'backward';
  status: 'normal' | 'warning' | 'error';
  faultInfo?: FaultInfo; // 故障信息
}

interface FacilityData {
  id: string;
  type:
    | 'power_plant'
    | 'substation'
    | 'storage'
    | 'heat_user'
    | 'gas_user'
    | 'industrial_user';
  coordinates: [number, number];
  name: string;
  status: 'running' | 'stopped' | 'maintenance' | 'error' | 'warning';
  capacity?: number;
  currentOutput?: number;
  faultInfo?: FaultInfo; // 故障信息
}

const EnergyNetworkMap: React.FC<EnergyNetworkMapProps> = ({ className }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showHeatNetwork, setShowHeatNetwork] = useState(true);
  const [showGasNetwork, setShowGasNetwork] = useState(true);
  const [showFacilities, setShowFacilities] = useState(true);
  const [selectedFacility, setSelectedFacility] = useState<FacilityData | null>(
    null,
  );
  const [hoverPopup, setHoverPopup] = useState<{
    visible: boolean;
    x: number;
    y: number;
    faultInfo: FaultInfo | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    faultInfo: null,
  });

  // 模拟管道数据
  const [pipelineData] = useState<PipelineData[]>([
    // 热网主管道
    {
      id: 'heat_main_1',
      type: 'heat',
      coordinates: [
        [116.3974, 39.9093],
        [116.3984, 39.9103],
        [116.3994, 39.9113],
        [116.4004, 39.9123],
      ],
      flow: 45.2,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_main_2',
      type: 'heat',
      coordinates: [
        [116.3954, 39.9073],
        [116.3964, 39.9083],
        [116.3974, 39.9093],
        [116.3984, 39.9103],
      ],
      flow: 38.7,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_branch_1',
      type: 'heat',
      coordinates: [
        [116.3984, 39.9103],
        [116.3994, 39.9093],
        [116.4004, 39.9083],
      ],
      flow: 18.5,
      direction: 'backward',
      status: 'warning',
      faultInfo: {
        level: 2,
        title: '二级响应',
        occurTime: '2024-01-15 14:30:25',
        description: '管道温度偏高，超出正常范围5℃',
        location: '热网支线1号-管段HB001',
        currentStatus: '处理中',
      },
    },
    {
      id: 'heat_branch_2',
      type: 'heat',
      coordinates: [
        [116.3994, 39.9113],
        [116.4014, 39.9103],
        [116.4024, 39.9093],
      ],
      flow: 22.3,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'heat_branch_3',
      type: 'heat',
      coordinates: [
        [116.3964, 39.9083],
        [116.3954, 39.9093],
        [116.3944, 39.9103],
      ],
      flow: 0,
      direction: 'forward',
      status: 'error',
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 16:45:12',
        description: '管道流量中断，疑似管道破裂',
        location: '热网支线3号-管段HB003',
        currentStatus: '待处理',
      },
    },
    // 气网主管道
    {
      id: 'gas_main_1',
      type: 'gas',
      coordinates: [
        [116.3964, 39.9083],
        [116.3974, 39.9093],
        [116.3984, 39.9103],
        [116.3994, 39.9113],
      ],
      flow: 32.8,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_main_2',
      type: 'gas',
      coordinates: [
        [116.3944, 39.9063],
        [116.3954, 39.9073],
        [116.3964, 39.9083],
        [116.3974, 39.9093],
      ],
      flow: 28.4,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_branch_1',
      type: 'gas',
      coordinates: [
        [116.3974, 39.9093],
        [116.3984, 39.9083],
        [116.3994, 39.9073],
      ],
      flow: 15.6,
      direction: 'forward',
      status: 'warning',
      faultInfo: {
        level: 3,
        title: '三级响应',
        occurTime: '2024-01-15 13:20:08',
        description: '管道压力轻微波动，在安全范围内',
        location: '气网支线1号-管段GB001',
        currentStatus: '处理中',
      },
    },
    {
      id: 'gas_branch_2',
      type: 'gas',
      coordinates: [
        [116.3984, 39.9103],
        [116.4004, 39.9113],
        [116.4014, 39.9123],
      ],
      flow: 19.2,
      direction: 'forward',
      status: 'normal',
    },
    {
      id: 'gas_branch_3',
      type: 'gas',
      coordinates: [
        [116.3954, 39.9073],
        [116.3944, 39.9083],
        [116.3934, 39.9093],
      ],
      flow: 12.8,
      direction: 'backward',
      status: 'normal',
    },
  ]);

  // 模拟设施数据
  const [facilityData] = useState<FacilityData[]>([
    // 发电设施
    {
      id: 'power_plant_1',
      type: 'power_plant',
      coordinates: [116.3974, 39.9093],
      name: '热电联产电厂1号',
      status: 'running',
      capacity: 100,
      currentOutput: 85,
    },
    {
      id: 'power_plant_2',
      type: 'power_plant',
      coordinates: [116.3954, 39.9073],
      name: '热电联产电厂2号',
      status: 'error',
      capacity: 80,
      currentOutput: 0,
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 15:22:35',
        description: '主机组故障停机，安全系统启动',
        location: '电厂2号-主机组MG002',
        currentStatus: '处理中',
      },
    },
    // 变电设施
    {
      id: 'substation_1',
      type: 'substation',
      coordinates: [116.3994, 39.9113],
      name: '变电站1号',
      status: 'running',
    },
    {
      id: 'substation_2',
      type: 'substation',
      coordinates: [116.4014, 39.9123],
      name: '变电站2号',
      status: 'maintenance',
    },
    // 储能设施
    {
      id: 'storage_1',
      type: 'storage',
      coordinates: [116.4004, 39.9083],
      name: '储能站1号',
      status: 'maintenance',
    },
    {
      id: 'storage_2',
      type: 'storage',
      coordinates: [116.3944, 39.9063],
      name: '储能站2号',
      status: 'running',
      capacity: 50,
      currentOutput: 25,
    },
    // 热用户
    {
      id: 'heat_user_1',
      type: 'heat_user',
      coordinates: [116.4004, 39.9123],
      name: '办公楼A栋',
      status: 'running',
      capacity: 15,
      currentOutput: 12,
    },
    {
      id: 'heat_user_2',
      type: 'heat_user',
      coordinates: [116.4024, 39.9093],
      name: '住宅区1号',
      status: 'running',
      capacity: 25,
      currentOutput: 18,
    },
    {
      id: 'heat_user_3',
      type: 'heat_user',
      coordinates: [116.3944, 39.9103],
      name: '商业中心',
      status: 'error',
      capacity: 30,
      currentOutput: 0,
      faultInfo: {
        level: 2,
        title: '二级响应',
        occurTime: '2024-01-15 12:15:42',
        description: '供热系统循环泵故障，影响供热效果',
        location: '商业中心-循环泵CP001',
        currentStatus: '待处理',
      },
    },
    {
      id: 'heat_user_4',
      type: 'heat_user',
      coordinates: [116.3934, 39.9093],
      name: '工厂厂房1号',
      status: 'running',
      capacity: 40,
      currentOutput: 35,
    },
    // 气用户
    {
      id: 'gas_user_1',
      type: 'gas_user',
      coordinates: [116.3994, 39.9073],
      name: '食堂燃气站',
      status: 'running',
      capacity: 8,
      currentOutput: 6,
    },
    {
      id: 'gas_user_2',
      type: 'gas_user',
      coordinates: [116.4014, 39.9103],
      name: '锅炉房1号',
      status: 'running',
      capacity: 20,
      currentOutput: 16,
    },
    {
      id: 'gas_user_3',
      type: 'gas_user',
      coordinates: [116.3944, 39.9083],
      name: '实验楼燃气站',
      status: 'warning',
      capacity: 12,
      currentOutput: 8,
      faultInfo: {
        level: 3,
        title: '三级响应',
        occurTime: '2024-01-15 11:45:18',
        description: '燃气表读数异常，需要校准',
        location: '实验楼-燃气表GM003',
        currentStatus: '处理中',
      },
    },
    // 工业用户
    {
      id: 'industrial_user_1',
      type: 'industrial_user',
      coordinates: [116.4024, 39.9113],
      name: '生产车间A',
      status: 'running',
      capacity: 60,
      currentOutput: 45,
    },
    {
      id: 'industrial_user_2',
      type: 'industrial_user',
      coordinates: [116.3924, 39.9083],
      name: '生产车间B',
      status: 'stopped',
      capacity: 50,
      currentOutput: 0,
    },
    {
      id: 'industrial_user_3',
      type: 'industrial_user',
      coordinates: [116.4034, 39.9103],
      name: '化工装置1号',
      status: 'error',
      capacity: 80,
      currentOutput: 0,
      faultInfo: {
        level: 1,
        title: '一级响应',
        occurTime: '2024-01-15 09:30:15',
        description: '反应器温度过高，紧急停机',
        location: '化工装置1号-反应器R001',
        currentStatus: '处理中',
      },
    },
  ]);

  // 实时数据状态
  const [realTimeData, setRealTimeData] = useState({
    pipelines: pipelineData,
    facilities: facilityData,
  });

  // 初始化地图图层
  function initializeMapLayers() {
    if (!map.current) return;

    addGridLayer();
    addPipelineLayers();
    addFacilityLayers();
  }

  useEffect(() => {
    if (!mapContainer.current) return;

    // 初始化地图
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/dark-v11',
      center: [116.3984, 39.9098],
      zoom: 16,
      pitch: 0,
      bearing: 0,
    });

    map.current.on('load', () => {
      setIsLoaded(true);
      initializeMapLayers();
      addMapInteractions();
    });

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  // 实时数据更新
  useEffect(() => {
    if (!isLoaded) return;

    const updateInterval = setInterval(() => {
      // 模拟实时数据更新
      const updatedPipelines = realTimeData.pipelines.map((pipeline) => ({
        ...pipeline,
        flow: Math.max(0, pipeline.flow + (Math.random() - 0.5) * 5), // 随机波动，确保非负
        status: Math.random() > 0.95 ? 'warning' : pipeline.status, // 偶尔出现警告
      }));

      const updatedFacilities = realTimeData.facilities.map((facility) => ({
        ...facility,
        currentOutput: facility.currentOutput
          ? Math.max(0, facility.currentOutput + (Math.random() - 0.5) * 10)
          : undefined,
        status: Math.random() > 0.98 ? 'error' : facility.status, // 偶尔出现故障
      }));

      setRealTimeData({
        pipelines: updatedPipelines,
        facilities: updatedFacilities,
      });

      // 更新地图数据
      updateMapData(updatedPipelines, updatedFacilities);
    }, 3000); // 每3秒更新一次

    return () => clearInterval(updateInterval);
  }, [isLoaded, realTimeData]);

  // 更新地图数据
  const updateMapData = (
    pipelines: PipelineData[],
    facilities: FacilityData[],
  ) => {
    if (!map.current) return;

    // 更新热网管道数据
    const heatPipelines = pipelines.filter((p) => p.type === 'heat');
    if (heatPipelines.length > 0 && map.current.getSource('heat-pipelines')) {
      (
        map.current.getSource('heat-pipelines') as mapboxgl.GeoJSONSource
      ).setData({
        type: 'FeatureCollection',
        features: heatPipelines.map((pipeline) => ({
          type: 'Feature' as const,
          properties: {
            id: pipeline.id,
            flow: pipeline.flow,
            direction: pipeline.direction,
            status: pipeline.status,
          },
          geometry: {
            type: 'LineString' as const,
            coordinates: pipeline.coordinates,
          },
        })),
      });

      // 更新流量标签
      if (map.current.getSource('heat-pipelines-labels')) {
        (
          map.current.getSource(
            'heat-pipelines-labels',
          ) as mapboxgl.GeoJSONSource
        ).setData({
          type: 'FeatureCollection',
          features: heatPipelines.map((pipeline) => {
            const coords = pipeline.coordinates;
            const midIndex = Math.floor(coords.length / 2);
            const midPoint = coords[midIndex];
            return {
              type: 'Feature' as const,
              properties: {
                id: pipeline.id,
                label: `${pipeline.flow.toFixed(1)} GJ/h`,
              },
              geometry: {
                type: 'Point' as const,
                coordinates: midPoint,
              },
            };
          }),
        });
      }
    }

    // 更新气网管道数据
    const gasPipelines = pipelines.filter((p) => p.type === 'gas');
    if (gasPipelines.length > 0 && map.current.getSource('gas-pipelines')) {
      (
        map.current.getSource('gas-pipelines') as mapboxgl.GeoJSONSource
      ).setData({
        type: 'FeatureCollection',
        features: gasPipelines.map((pipeline) => ({
          type: 'Feature' as const,
          properties: {
            id: pipeline.id,
            flow: pipeline.flow,
            direction: pipeline.direction,
            status: pipeline.status,
          },
          geometry: {
            type: 'LineString' as const,
            coordinates: pipeline.coordinates,
          },
        })),
      });

      // 更新流量标签
      if (map.current.getSource('gas-pipelines-labels')) {
        (
          map.current.getSource(
            'gas-pipelines-labels',
          ) as mapboxgl.GeoJSONSource
        ).setData({
          type: 'FeatureCollection',
          features: gasPipelines.map((pipeline) => {
            const coords = pipeline.coordinates;
            const midIndex = Math.floor(coords.length / 2);
            const midPoint = coords[midIndex];
            return {
              type: 'Feature' as const,
              properties: {
                id: pipeline.id,
                label: `${pipeline.flow.toFixed(1)} m³/h`,
              },
              geometry: {
                type: 'Point' as const,
                coordinates: midPoint,
              },
            };
          }),
        });
      }
    }

    // 更新设施数据
    if (map.current.getSource('facilities')) {
      (map.current.getSource('facilities') as mapboxgl.GeoJSONSource).setData({
        type: 'FeatureCollection',
        features: facilities.map((facility) => ({
          type: 'Feature' as const,
          properties: {
            id: facility.id,
            type: facility.type,
            name: facility.name,
            status: facility.status,
            capacity: facility.capacity,
            currentOutput: facility.currentOutput,
          },
          geometry: {
            type: 'Point' as const,
            coordinates: facility.coordinates,
          },
        })),
      });
    }
  };

  // 添加1km×1km网格图层
  function addGridLayer() {
    if (!map.current) return;

    const gridSize = 0.01; // 约1km
    const centerLng = 116.3984;
    const centerLat = 39.9098;
    const gridLines: any[] = [];

    // 创建垂直线
    for (let i = -5; i <= 5; i++) {
      const lng = centerLng + i * gridSize;
      gridLines.push({
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: [
            [lng, centerLat - 5 * gridSize],
            [lng, centerLat + 5 * gridSize],
          ],
        },
      });
    }

    // 创建水平线
    for (let i = -5; i <= 5; i++) {
      const lat = centerLat + i * gridSize;
      gridLines.push({
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: [
            [centerLng - 5 * gridSize, lat],
            [centerLng + 5 * gridSize, lat],
          ],
        },
      });
    }

    map.current.addSource('grid', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: gridLines,
      },
    });

    map.current.addLayer({
      id: 'grid-lines',
      type: 'line',
      source: 'grid',
      paint: {
        'line-color': 'rgba(255, 255, 255, 0.1)',
        'line-width': 1,
      },
    });
  }

  // 添加管道图层
  function addPipelineLayers() {
    if (!map.current) return;

    const heatPipelines = pipelineData.filter((p) => p.type === 'heat');
    const gasPipelines = pipelineData.filter((p) => p.type === 'gas');

    // 添加热网管道
    if (heatPipelines.length > 0) {
      map.current.addSource('heat-pipelines', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: heatPipelines.map((pipeline) => ({
            type: 'Feature',
            properties: {
              id: pipeline.id,
              flow: pipeline.flow,
              direction: pipeline.direction,
              status: pipeline.status,
            },
            geometry: {
              type: 'LineString',
              coordinates: pipeline.coordinates,
            },
          })),
        },
      });

      map.current.addLayer({
        id: 'heat-pipelines',
        type: 'line',
        source: 'heat-pipelines',
        paint: {
          'line-color': [
            'case',
            ['==', ['get', 'status'], 'error'],
            '#ff4d4f',
            ['==', ['get', 'status'], 'warning'],
            '#faad14',
            '#ff7875',
          ],
          'line-width': 4,
          'line-opacity': 0.8,
        },
      });

      // 添加流量标签和流向箭头
      addFlowLabels('heat-pipelines', heatPipelines, 'GJ/h');
      addFlowArrows('heat-pipelines', heatPipelines, '#ff7875');
    }

    // 添加气网管道
    if (gasPipelines.length > 0) {
      map.current.addSource('gas-pipelines', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: gasPipelines.map((pipeline) => ({
            type: 'Feature',
            properties: {
              id: pipeline.id,
              flow: pipeline.flow,
              direction: pipeline.direction,
              status: pipeline.status,
            },
            geometry: {
              type: 'LineString',
              coordinates: pipeline.coordinates,
            },
          })),
        },
      });

      map.current.addLayer({
        id: 'gas-pipelines',
        type: 'line',
        source: 'gas-pipelines',
        paint: {
          'line-color': [
            'case',
            ['==', ['get', 'status'], 'error'],
            '#ff4d4f',
            ['==', ['get', 'status'], 'warning'],
            '#faad14',
            '#52c41a',
          ],
          'line-width': 4,
          'line-opacity': 0.8,
        },
      });

      // 添加流量标签和流向箭头
      addFlowLabels('gas-pipelines', gasPipelines, 'm³/h');
      addFlowArrows('gas-pipelines', gasPipelines, '#52c41a');
    }
  }

  // 添加流向箭头
  const addFlowArrows = (
    sourceId: string,
    pipelines: PipelineData[],
    color: string,
  ) => {
    if (!map.current) return;

    const arrowFeatures = pipelines.map((pipeline) => {
      const coords = pipeline.coordinates;
      const midIndex = Math.floor(coords.length / 2);
      const midPoint = coords[midIndex];

      // 计算箭头方向
      const prevPoint = coords[midIndex - 1] || coords[0];
      const nextPoint = coords[midIndex + 1] || coords[coords.length - 1];

      const angle =
        (Math.atan2(nextPoint[1] - prevPoint[1], nextPoint[0] - prevPoint[0]) *
          180) /
        Math.PI;

      return {
        type: 'Feature' as const,
        properties: {
          id: pipeline.id,
          direction: pipeline.direction,
          angle: pipeline.direction === 'backward' ? angle + 180 : angle,
        },
        geometry: {
          type: 'Point' as const,
          coordinates: midPoint,
        },
      };
    });

    map.current.addSource(`${sourceId}-arrows`, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: arrowFeatures,
      },
    });

    map.current.addLayer({
      id: `${sourceId}-arrows`,
      type: 'symbol',
      source: `${sourceId}-arrows`,
      layout: {
        'text-field': '▶',
        'text-font': ['Open Sans Regular'],
        'text-size': 16,
        'text-rotate': ['get', 'angle'],
        'text-rotation-alignment': 'map',
        'text-allow-overlap': true,
      },
      paint: {
        'text-color': color,
        'text-halo-color': '#000000',
        'text-halo-width': 1,
      },
    });
  };

  // 添加流量标签
  const addFlowLabels = (
    sourceId: string,
    pipelines: PipelineData[],
    unit: string,
  ) => {
    if (!map.current) return;

    const labelFeatures = pipelines.map((pipeline) => {
      const coords = pipeline.coordinates;
      const midIndex = Math.floor(coords.length / 2);
      const midPoint = coords[midIndex];

      return {
        type: 'Feature' as const,
        properties: {
          id: pipeline.id,
          label: `${pipeline.flow} ${unit}`,
        },
        geometry: {
          type: 'Point' as const,
          coordinates: midPoint,
        },
      };
    });

    map.current.addSource(`${sourceId}-labels`, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: labelFeatures,
      },
    });

    map.current.addLayer({
      id: `${sourceId}-labels`,
      type: 'symbol',
      source: `${sourceId}-labels`,
      layout: {
        'text-field': ['get', 'label'],
        'text-font': ['Open Sans Regular'],
        'text-size': 12,
        'text-offset': [0, -2],
        'text-anchor': 'bottom',
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 2,
      },
    });
  };

  // 添加设施图层
  function addFacilityLayers() {
    if (!map.current) return;

    map.current.addSource('facilities', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: facilityData.map((facility) => ({
          type: 'Feature',
          properties: {
            id: facility.id,
            type: facility.type,
            name: facility.name,
            status: facility.status,
            capacity: facility.capacity,
            currentOutput: facility.currentOutput,
          },
          geometry: {
            type: 'Point',
            coordinates: facility.coordinates,
          },
        })),
      },
    });

    map.current.addLayer({
      id: 'facilities',
      type: 'circle',
      source: 'facilities',
      paint: {
        'circle-radius': [
          'case',
          ['==', ['get', 'type'], 'power_plant'],
          12,
          ['==', ['get', 'type'], 'substation'],
          8,
          6,
        ],
        'circle-color': [
          'case',
          ['==', ['get', 'status'], 'error'],
          '#ff4d4f',
          ['==', ['get', 'status'], 'maintenance'],
          '#faad14',
          ['==', ['get', 'status'], 'stopped'],
          '#8c8c8c',
          '#52c41a',
        ],
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff',
      },
    });

    // 添加设施标签
    map.current.addLayer({
      id: 'facility-labels',
      type: 'symbol',
      source: 'facilities',
      layout: {
        'text-field': ['get', 'name'],
        'text-font': ['Open Sans Regular'],
        'text-offset': [0, 2],
        'text-anchor': 'top',
        'text-size': 12,
      },
      paint: {
        'text-color': '#ffffff',
        'text-halo-color': '#000000',
        'text-halo-width': 1,
      },
    });
  }

  // 添加地图交互
  const addMapInteractions = () => {
    if (!map.current) return;

    // 管道点击事件
    map.current.on('click', 'heat-pipelines', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        new mapboxgl.Popup()
          .setLngLat(e.lngLat)
          .setHTML(
            `
            <div style="color: #000; padding: 10px;">
              <h4>热网管道</h4>
              <p>流量: ${properties?.flow} GJ/h</p>
              <p>状态: ${getStatusText(properties?.status)}</p>
            </div>
          `,
          )
          .addTo(map.current!);
      }
    });

    map.current.on('click', 'gas-pipelines', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        new mapboxgl.Popup()
          .setLngLat(e.lngLat)
          .setHTML(
            `
            <div style="color: #000; padding: 10px;">
              <h4>气网管道</h4>
              <p>流量: ${properties?.flow} m³/h</p>
              <p>状态: ${getStatusText(properties?.status)}</p>
            </div>
          `,
          )
          .addTo(map.current!);
      }
    });

    map.current.on('click', 'facilities', (e) => {
      if (e.features && e.features[0]) {
        const properties = e.features[0].properties;
        const facility = facilityData.find((f) => f.id === properties?.id);
        if (facility) {
          setSelectedFacility(facility);
        }
      }
    });

    // 设施悬浮事件
    map.current.on('mouseenter', 'facilities', (e) => {
      if (map.current && e.features && e.features[0]) {
        map.current.getCanvas().style.cursor = 'pointer';

        const feature = e.features[0];
        const facility = facilityData.find(
          (f) => f.id === feature.properties?.id,
        );

        if (
          facility &&
          facility.faultInfo &&
          (facility.status === 'error' || facility.status === 'warning')
        ) {
          const canvas = map.current.getCanvas();
          const rect = canvas.getBoundingClientRect();

          setHoverPopup({
            visible: true,
            x: e.point.x + rect.left,
            y: e.point.y + rect.top,
            faultInfo: facility.faultInfo,
          });
        }
      }
    });

    map.current.on('mouseleave', 'facilities', () => {
      if (map.current) {
        map.current.getCanvas().style.cursor = '';
        setHoverPopup({
          visible: false,
          x: 0,
          y: 0,
          faultInfo: null,
        });
      }
    });

    // 管道悬浮事件
    map.current.on('mouseenter', 'heat-pipelines', (e) => {
      if (map.current && e.features && e.features[0]) {
        map.current.getCanvas().style.cursor = 'pointer';

        const feature = e.features[0];
        const pipeline = pipelineData.find(
          (p) => p.id === feature.properties?.id,
        );

        if (
          pipeline &&
          pipeline.faultInfo &&
          (pipeline.status === 'error' || pipeline.status === 'warning')
        ) {
          const canvas = map.current.getCanvas();
          const rect = canvas.getBoundingClientRect();

          setHoverPopup({
            visible: true,
            x: e.point.x + rect.left,
            y: e.point.y + rect.top,
            faultInfo: pipeline.faultInfo,
          });
        }
      }
    });

    map.current.on('mouseleave', 'heat-pipelines', () => {
      if (map.current) {
        map.current.getCanvas().style.cursor = '';
        setHoverPopup({
          visible: false,
          x: 0,
          y: 0,
          faultInfo: null,
        });
      }
    });

    map.current.on('mouseenter', 'gas-pipelines', (e) => {
      if (map.current && e.features && e.features[0]) {
        map.current.getCanvas().style.cursor = 'pointer';

        const feature = e.features[0];
        const pipeline = pipelineData.find(
          (p) => p.id === feature.properties?.id,
        );

        if (
          pipeline &&
          pipeline.faultInfo &&
          (pipeline.status === 'error' || pipeline.status === 'warning')
        ) {
          const canvas = map.current.getCanvas();
          const rect = canvas.getBoundingClientRect();

          setHoverPopup({
            visible: true,
            x: e.point.x + rect.left,
            y: e.point.y + rect.top,
            faultInfo: pipeline.faultInfo,
          });
        }
      }
    });

    map.current.on('mouseleave', 'gas-pipelines', () => {
      if (map.current) {
        map.current.getCanvas().style.cursor = '';
        setHoverPopup({
          visible: false,
          x: 0,
          y: 0,
          faultInfo: null,
        });
      }
    });
  };

  // 状态文本转换
  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'warning':
        return '警告';
      case 'error':
        return '故障';
      case 'running':
        return '运行中';
      case 'stopped':
        return '停止';
      case 'maintenance':
        return '维护中';
      default:
        return '未知';
    }
  };

  // 设施类型文本转换
  const getFacilityTypeText = (type: string) => {
    switch (type) {
      case 'power_plant':
        return '热电联产电厂';
      case 'substation':
        return '变电站';
      case 'storage':
        return '储能站';
      case 'heat_user':
        return '热用户';
      case 'gas_user':
        return '气用户';
      case 'industrial_user':
        return '工业用户';
      default:
        return '未知设施';
    }
  };

  // 切换图层显示
  const toggleLayer = (layerType: 'heat' | 'gas' | 'facilities') => {
    if (!map.current) return;

    switch (layerType) {
      case 'heat':
        setShowHeatNetwork(!showHeatNetwork);
        const heatVisibility = showHeatNetwork ? 'none' : 'visible';
        map.current.setLayoutProperty(
          'heat-pipelines',
          'visibility',
          heatVisibility,
        );
        map.current.setLayoutProperty(
          'heat-pipelines-arrows',
          'visibility',
          heatVisibility,
        );
        map.current.setLayoutProperty(
          'heat-pipelines-labels',
          'visibility',
          heatVisibility,
        );
        break;
      case 'gas':
        setShowGasNetwork(!showGasNetwork);
        const gasVisibility = showGasNetwork ? 'none' : 'visible';
        map.current.setLayoutProperty(
          'gas-pipelines',
          'visibility',
          gasVisibility,
        );
        map.current.setLayoutProperty(
          'gas-pipelines-arrows',
          'visibility',
          gasVisibility,
        );
        map.current.setLayoutProperty(
          'gas-pipelines-labels',
          'visibility',
          gasVisibility,
        );
        break;
      case 'facilities':
        setShowFacilities(!showFacilities);
        const facilityVisibility = showFacilities ? 'none' : 'visible';
        map.current.setLayoutProperty(
          'facilities',
          'visibility',
          facilityVisibility,
        );
        map.current.setLayoutProperty(
          'facility-labels',
          'visibility',
          facilityVisibility,
        );
        break;
    }
  };

  return (
    <div className={`${styles.mapContainer} ${className || ''}`}>
      <div ref={mapContainer} className={styles.map} />

      {/* 控制面板 */}
      {isLoaded && (
        <div className={styles.controlPanel}>
          <button
            className={`${styles.controlButton} ${
              showHeatNetwork ? styles.active : ''
            }`}
            onClick={() => toggleLayer('heat')}
            title="热网系统"
          >
            🔥
          </button>
          <button
            className={`${styles.controlButton} ${
              showGasNetwork ? styles.active : ''
            }`}
            onClick={() => toggleLayer('gas')}
            title="气网系统"
          >
            ⛽
          </button>
          <button
            className={`${styles.controlButton} ${
              showFacilities ? styles.active : ''
            }`}
            onClick={() => toggleLayer('facilities')}
            title="设施标记"
          >
            🏭
          </button>
        </div>
      )}

      {/* 图例 */}
      {isLoaded && (
        <div className={styles.legend}>
          <div className={styles.legendTitle}>图例</div>
          <div className={styles.legendItem}>
            <div className={`${styles.legendIcon} ${styles.heat}`}></div>
            <span>热网管道</span>
          </div>
          <div className={styles.legendItem}>
            <div className={`${styles.legendIcon} ${styles.gas}`}></div>
            <span>气网管道</span>
          </div>
          <div className={styles.legendItem}>
            <div className={`${styles.legendDot} ${styles.running}`}></div>
            <span>运行中</span>
          </div>
          <div className={styles.legendItem}>
            <div className={`${styles.legendDot} ${styles.maintenance}`}></div>
            <span>维护中</span>
          </div>
          <div className={styles.legendItem}>
            <div className={`${styles.legendDot} ${styles.error}`}></div>
            <span>故障</span>
          </div>
        </div>
      )}

      {/* 信息面板 */}
      {selectedFacility && (
        <div className={styles.infoPanel}>
          <div className={styles.infoPanelTitle}>{selectedFacility.name}</div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>类型:</span>
            <span className={styles.infoValue}>
              {getFacilityTypeText(selectedFacility.type)}
            </span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>状态:</span>
            <span
              className={`${styles.infoValue} ${
                styles[selectedFacility.status]
              }`}
            >
              {getStatusText(selectedFacility.status)}
            </span>
          </div>
          {selectedFacility.capacity && (
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>容量:</span>
              <span className={styles.infoValue}>
                {selectedFacility.capacity} MW
              </span>
            </div>
          )}
          {selectedFacility.currentOutput && (
            <div className={styles.infoItem}>
              <span className={styles.infoLabel}>当前输出:</span>
              <span className={styles.infoValue}>
                {selectedFacility.currentOutput} MW
              </span>
            </div>
          )}
          <button
            onClick={() => setSelectedFacility(null)}
            style={{
              position: 'absolute',
              top: '5px',
              right: '5px',
              background: 'none',
              border: 'none',
              color: '#fff',
              cursor: 'pointer',
            }}
          >
            ✕
          </button>
        </div>
      )}

      {/* 故障悬浮弹窗 */}
      {hoverPopup.visible && hoverPopup.faultInfo && (
        <div
          className={styles.faultPopup}
          style={{
            position: 'fixed',
            left: `${hoverPopup.x + 10}px`,
            top: `${hoverPopup.y - 10}px`,
            zIndex: 1000,
            pointerEvents: 'none',
          }}
        >
          <div className={styles.faultPopupContent}>
            <div
              className={`${styles.faultTitle} ${
                styles[`level${hoverPopup.faultInfo.level}`]
              }`}
            >
              {hoverPopup.faultInfo.title}
            </div>
            <div className={styles.faultTime}>
              发生时间: {hoverPopup.faultInfo.occurTime}
            </div>
            <div className={styles.faultDescription}>
              {hoverPopup.faultInfo.description}
            </div>
            <div className={styles.faultLocation}>
              位置: {hoverPopup.faultInfo.location}
            </div>
            <div className={styles.faultStatus}>
              状态: {hoverPopup.faultInfo.currentStatus}
            </div>
          </div>
        </div>
      )}

      {!isLoaded && (
        <div className={styles.loading}>
          <div className={styles.loadingText}>地图加载中...</div>
        </div>
      )}
    </div>
  );
};

export default EnergyNetworkMap;
